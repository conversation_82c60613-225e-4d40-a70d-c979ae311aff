<?php
/**
 * ระบบนับยอดวิวหนัง - เวอร์ชันแก้ไขแล้ว
 * แก้ไขปัญหาการไม่อัปเดตยอดวิว
 */

// 1. ฟังก์ชันดึงจำนวน views (ปรับปรุงแล้ว)
function get_movie_views($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    // ตรวจสอบว่า post_id ถูกต้อง
    if (!$post_id || !is_numeric($post_id)) {
        return 0;
    }
    
    $views = get_post_meta($post_id, 'movie_views', true);
    return $views ? intval($views) : 0;
}

// 2. ฟังก์ชันเพิ่มยอดวิว (ปรับปรุงแล้ว)
function add_movie_view($post_id) {
    // ตรวจสอบ post_id
    if (!$post_id || !is_numeric($post_id)) {
        return false;
    }
    
    // ตรวจสอบว่า post มีอยู่จริง
    if (!get_post($post_id)) {
        return false;
    }
    
    $views = get_movie_views($post_id);
    $views++;
    
    // บันทึกข้อมูล
    $result = update_post_meta($post_id, 'movie_views', $views);
    
    // Log สำหรับ debug
    error_log("Movie Views Update - Post ID: $post_id, New Views: $views, Result: " . ($result ? 'Success' : 'Failed'));
    
    return $views;
}

// 3. ฟังก์ชันแปลงตัวเลข (เหมือนเดิม)
function format_movie_views($views) {
    if ($views >= 1000000) {
        return number_format($views / 1000000, 1) . 'M';
    } elseif ($views >= 1000) {
        return number_format($views / 1000, 1) . 'K';
    } else {
        return number_format($views);
    }
}

// 4. ฟังก์ชันแสดง views counter (ปรับปรุงแล้ว)
function display_movie_views($post_id = null, $style = 1) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    if (!$post_id) {
        return '<span class="box-movie-views eye-only" data-views="0" title="ไม่พบข้อมูล">👁</span>';
    }
    
    $views = get_movie_views($post_id);
    
    // ถ้ายอดวิวเป็น 0 ให้แสดงแค่รูปตา
    if ($views == 0) {
        return '<span class="box-movie-views eye-only" data-views="0" data-post-id="' . $post_id . '" title="ยังไม่มีการดู">👁</span>';
    }
    
    // ถ้ามียอดวิวให้แสดงตัวเลข
    $formatted_views = format_movie_views($views);
    return '<span class="box-movie-views" data-views="' . $views . '" data-post-id="' . $post_id . '" title="' . number_format($views) . ' การดู">' . $formatted_views . '</span>';
}

// 5. AJAX สำหรับนับ views เมื่อคลิก (ปรับปรุงแล้ว)
function ajax_track_movie_click() {
    // ตรวจสอบ nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'movie_click_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
    if (!$post_id) {
        wp_send_json_error('Invalid post ID');
        return;
    }

    // ตรวจสอบว่า post มีอยู่จริง
    if (!get_post($post_id)) {
        wp_send_json_error('Post not found');
        return;
    }

    // ไม่นับ admin (เฉพาะที่ login)
    if (is_user_logged_in() && current_user_can('administrator')) {
        wp_send_json_success(array(
            'message' => 'Admin - ไม่นับ',
            'views' => get_movie_views($post_id),
            'formatted' => format_movie_views(get_movie_views($post_id)),
            'post_id' => $post_id
        ));
        return;
    }

    // เพิ่มยอดวิว
    $new_views = add_movie_view($post_id);
    
    if ($new_views === false) {
        wp_send_json_error('Failed to update views');
        return;
    }
    
    $formatted_views = format_movie_views($new_views);

    // ข้อมูลสำหรับ debug
    $user_ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

    wp_send_json_success(array(
        'message' => 'เพิ่มยอดวิวแล้ว',
        'views' => $new_views,
        'formatted' => $formatted_views,
        'post_id' => $post_id,
        'ip' => $user_ip,
        'device' => (strpos($user_agent, 'iPad') !== false ? 'iPad' : (strpos($user_agent, 'iPhone') !== false ? 'iPhone' : (strpos($user_agent, 'Android') !== false ? 'Android' : 'Desktop')))
    ));
}
add_action('wp_ajax_track_movie_click', 'ajax_track_movie_click');
add_action('wp_ajax_nopriv_track_movie_click', 'ajax_track_movie_click');

// 6. JavaScript สำหรับจับการคลิกหนัง (ปรับปรุงแล้ว)
function add_click_tracking_script() {
    // ตรวจสอบว่า jQuery โหลดแล้ว
    if (!wp_script_is('jquery', 'enqueued')) {
        wp_enqueue_script('jquery');
    }
    ?>
    <script>
    jQuery(document).ready(function($) {
        console.log('🎬 Movie Click Tracker initializing...');

        // ตัวแปรสำหรับ AJAX
        var movieClickTracker = {
            ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('movie_click_nonce'); ?>'
        };

        console.log('📡 AJAX URL:', movieClickTracker.ajax_url);

        // ฟังก์ชันดึง Post ID จาก URL
        function extractPostId(url) {
            if (!url) return null;
            
            // วิธี 1: จาก URL pattern (/123/)
            var matches = url.match(/\/(\d+)\//);
            if (matches && matches[1]) {
                return matches[1];
            }

            // วิธี 2: จาก URL parameter (?p=123)
            try {
                var urlObj = new URL(url, window.location.origin);
                var postId = urlObj.searchParams.get('p') || urlObj.searchParams.get('post_id');
                if (postId) return postId;
            } catch(e) {
                console.log('URL parsing error:', e);
            }

            // วิธี 3: จาก slug pattern
            var slugMatch = url.match(/\/([^\/]+)\/?$/);
            if (slugMatch && slugMatch[1] && !isNaN(slugMatch[1])) {
                return slugMatch[1];
            }

            return null;
        }

        // จับการคลิกที่ลิงก์หนัง
        $(document).on('click', '.movie_box a, .grid-movie-index a, .grid-movie-advise a, a[href*="movie"], a[href*="film"]', function(e) {
            var $link = $(this);
            var href = $link.attr('href');

            console.log('🖱️ คลิกลิงก์:', href);

            // ดึง post ID จาก URL
            var postId = extractPostId(href);

            // วิธี 4: จาก data attribute
            if (!postId) {
                postId = $link.data('post-id') || 
                         $link.closest('.movie_box').data('post-id') ||
                         $link.find('[data-post-id]').data('post-id');
            }

            // วิธี 5: จาก views counter
            if (!postId) {
                var $viewsCounter = $link.find('.box-movie-views');
                if ($viewsCounter.length > 0) {
                    postId = $viewsCounter.data('post-id');
                }
            }

            console.log('🆔 Post ID ที่พบ:', postId);

            if (postId && postId != '0') {
                // ส่ง AJAX เพื่อนับ view
                $.ajax({
                    url: movieClickTracker.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'track_movie_click',
                        post_id: postId,
                        nonce: movieClickTracker.nonce
                    },
                    timeout: 5000, // 5 วินาที timeout
                    success: function(response) {
                        console.log('📡 AJAX Response:', response);
                        
                        if (response.success) {
                            console.log('✅ ' + response.data.message);
                            console.log('👁️ ยอดวิวใหม่: ' + response.data.views);
                            console.log('📱 อุปกรณ์: ' + response.data.device);

                            // อัพเดท views counter ในหน้า (ถ้ามี)
                            var $viewsCounter = $link.find('.box-movie-views');
                            if ($viewsCounter.length > 0) {
                                if (response.data.views > 0) {
                                    $viewsCounter.removeClass('eye-only').text(response.data.formatted);
                                    $viewsCounter.attr('title', response.data.views + ' การดู');
                                } else {
                                    $viewsCounter.addClass('eye-only').text('👁');
                                }
                                $viewsCounter.attr('data-views', response.data.views);
                            }
                        } else {
                            console.log('❌ Error: ' + (response.data || 'Unknown error'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('❌ AJAX Error:', status, error);
                        console.log('Response:', xhr.responseText);
                    }
                });
            } else {
                console.log('⚠️ ไม่พบ Post ID ใน URL: ' + href);
            }
        });

        console.log('✅ Movie Click Tracker initialized successfully');
    });
    </script>
    <?php
}
add_action('wp_footer', 'add_click_tracking_script');

// 7. ฟังก์ชันทดสอบระบบ (เพิ่มใหม่)
function test_movie_views_system() {
    if (!current_user_can('administrator')) {
        return;
    }
    
    echo '<div style="background: #fff; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
    echo '<h3>🧪 ทดสอบระบบนับยอดวิว</h3>';
    
    // ทดสอบ AJAX URL
    echo '<p><strong>AJAX URL:</strong> ' . admin_url('admin-ajax.php') . '</p>';
    
    // ทดสอบ Nonce
    echo '<p><strong>Nonce:</strong> ' . wp_create_nonce('movie_click_nonce') . '</p>';
    
    // ทดสอบ jQuery
    echo '<p><strong>jQuery:</strong> ' . (wp_script_is('jquery', 'enqueued') ? '✅ โหลดแล้ว' : '❌ ยังไม่โหลด') . '</p>';
    
    // ทดสอบกับ post ปัจจุบัน
    if (is_single()) {
        $post_id = get_the_ID();
        $views = get_movie_views($post_id);
        echo '<p><strong>Post ID ปัจจุบัน:</strong> ' . $post_id . '</p>';
        echo '<p><strong>ยอดวิวปัจจุบัน:</strong> ' . $views . '</p>';
        echo '<p><strong>แสดงผล:</strong> ' . display_movie_views($post_id) . '</p>';
    }
    
    echo '</div>';
}

// เพิ่มการทดสอบในหน้า admin (เฉพาะ admin)
if (is_admin() && current_user_can('administrator')) {
    add_action('admin_notices', 'test_movie_views_system');
}

?>
