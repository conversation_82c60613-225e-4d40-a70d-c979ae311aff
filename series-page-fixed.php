<?php get_header(); ?>
<main id="primary" class="site-main">
    <div class="grid-main">
        <?php include get_template_directory() . '/left-sidebar.php'; ?>
        <div id="main">
            <div class="update-movie">
                <h2>ดูซีรี่ส์ออนไลน์</h2>
            </div>
            <div class="grid-movie-index">
                <?php  
                // ตั้งค่า pagination
                $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;  
                
                // Query สำหรับซีรี่ส์
                $args = array(
                    'post_type' => 'serie', 
                    'paged' => $paged,
                    'posts_per_page' => 20, // จำนวนโพสต์ต่อหน้า
                    'post_status' => 'publish'
                );   
                
                $the_query = new WP_Query($args); 
                ?>
                
                <?php if ($the_query->have_posts()) : ?>
                    <?php while ($the_query->have_posts()) : $the_query->the_post(); ?> 
                        <div class="movie_box">
                            <a id="index-grid" href="<?php the_permalink(); ?>" data-post-id="<?php echo get_the_ID(); ?>">
                                <?php if (has_post_thumbnail()) { ?>
                                    <img src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'medium')); ?>" 
                                         alt="<?php the_title_attribute(); ?>" 
                                         loading="lazy">
                                <?php } else { ?>
                                    <img src="<?php echo get_template_directory_uri(); ?>/images/no-image.jpg" 
                                         alt="<?php the_title_attribute(); ?>" 
                                         loading="lazy">
                                <?php } ?>
                                
                                <p><?php 
                                    // ตรวจสอบว่ามีฟังก์ชัน custom_the_title หรือไม่
                                    if (function_exists('custom_the_title')) {
                                        custom_the_title(); 
                                    } else {
                                        the_title();
                                    }
                                ?></p>
                                
                                <span class="movie-lang"><?php 
                                    // ตรวจสอบว่ามีฟังก์ชัน get_audio_type หรือไม่
                                    if (function_exists('get_audio_type')) {
                                        echo get_audio_type(get_the_ID()); 
                                    } else {
                                        echo 'TH'; // ค่าเริ่มต้น
                                    }
                                ?></span>
                                
                                <div class="figure-box">
                                    <span class="box-movie-star"><?php 
                                        // ตรวจสอบว่ามีฟังก์ชัน display_imdb_rating หรือไม่
                                        if (function_exists('display_imdb_rating')) {
                                            display_imdb_rating(); 
                                        } else {
                                            echo '8.0'; // ค่าเริ่มต้น
                                        }
                                    ?></span>
                                    
                                    <span class="box-movie-hd">HD</span>
                                    
                                    <?php 
                                    // แสดงยอดวิว (ถ้ามีฟังก์ชัน)
                                    if (function_exists('display_movie_views')) {
                                        echo display_movie_views(get_the_ID(), 1); 
                                    }
                                    ?>
                                </div>
                            </a>
                        </div>
                    <?php endwhile; ?>
                    
                <?php else : ?>
                    <div class="no-posts">
                        <p>ไม่พบซีรี่ส์ที่ต้องการ</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <?php 
            // Pagination
            if (function_exists('custom_pagination')) {
                custom_pagination($the_query); 
            } else {
                // Pagination เริ่มต้น
                echo '<div class="pagination">';
                echo paginate_links(array(
                    'total' => $the_query->max_num_pages,
                    'current' => $paged,
                    'prev_text' => '« ก่อนหน้า',
                    'next_text' => 'ถัดไป »'
                ));
                echo '</div>';
            }
            
            // รีเซ็ตข้อมูล query
            wp_reset_postdata(); 
            ?>
        </div>

        <?php get_sidebar(); ?>
    </div>
</main>

<style>
/* CSS สำหรับ Views Counter */
.box-movie-views {
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin-left: 5px;
}

.box-movie-views[style*="display: none"] {
    display: none !important;
}

/* CSS สำหรับ No Posts */
.no-posts {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 16px;
}

/* CSS สำหรับ Pagination */
.pagination {
    text-align: center;
    margin: 30px 0;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    background: #f5f5f5;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
}

.pagination a:hover {
    background: #333;
    color: white;
}

.pagination .current {
    background: #333;
    color: white;
}

/* CSS สำหรับ Loading */
.movie_box img {
    transition: opacity 0.3s ease;
}

.movie_box img[loading="lazy"] {
    opacity: 0.8;
}

.movie_box:hover img {
    opacity: 1;
}
</style>

<script>
// JavaScript สำหรับ Debug
jQuery(document).ready(function($) {
    console.log('🎬 Series Page Loaded');
    console.log('📊 Total Movie Boxes:', $('.movie_box').length);
    console.log('👁️ Views Counters:', $('.box-movie-views').length);
    
    // ตรวจสอบลิงก์
    $('.movie_box a').each(function(index) {
        var href = $(this).attr('href');
        var postId = $(this).data('post-id');
        console.log('🔗 Link ' + (index + 1) + ':', href, 'Post ID:', postId);
    });
});
</script>

<?php get_footer(); ?>
